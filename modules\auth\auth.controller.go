package auth

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type AuthController struct {
}

func (m AuthController) Login(c core.IHTTPContext) error {
	input := &requests.AuthLogin{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	authSvc := services.NewAuthService(c)
	payload := &services.LoginPayload{
		Email:      *input.Email,
		Password:   *input.Password,
		IPAddress:  c.RealIP(),
		UserAgent:  c.Request().UserAgent(),
		DeviceInfo: c.Request().Header.Get("X-Device-Info"),
	}

	response, err := authSvc.Login(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSO<PERSON>())
	}

	return c.<PERSON>SO<PERSON>(http.StatusOK, response)
}

func (m AuthController) Logout(c core.IHTTPContext) error {
	authSvc := services.NewAuthService(c)
	err := authSvc.Logout(c.GetUser().Data["token"])
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
