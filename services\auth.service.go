package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/utils"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/oauth2"
)

type IAuthService interface {
	Login(input *LoginPayload) (*LoginResponse, core.IError)
	Logout(token string) core.IError
	SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError)
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) bool
	GenerateToken() (string, error)
	ValidateToken(token string) (*models.User, core.IError)
	CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError
	DeleteUserToken(token string) core.IError
}

type authService struct {
	ctx core.IContext
}

type LoginPayload struct {
	Email      string
	Password   string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type SlackCallbackPayload struct {
	Code       string
	State      string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type LoginResponse struct {
	User  *models.User `json:"user"`
	Token string       `json:"token"`
}

func (s authService) Login(input *LoginPayload) (*LoginResponse, core.IError) {
	// Find user by email
	user, ierr := repo.User(s.ctx).Where("is_active = ?", true).FindOne("email = ?", input.Email)
	if ierr != nil {
		if errmsgs.IsNotFoundError(ierr) {
			ierr = core.Error{
				Status:  http.StatusUnauthorized,
				Code:    "INVALID_CREDENTIALS",
				Message: "Invalid email or password",
			}
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Verify password
	if !s.VerifyPassword(user.Password, input.Password) {
		ierr = core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "INVALID_CREDENTIALS",
			Message: "Invalid email or password",
		}

		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) Logout(token string) core.IError {
	return s.DeleteUserToken(token)
}

func (s authService) SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError) {
	// Exchange authorization code for access token
	slackUser, ierr := s.exchangeSlackCode(input.Code)
	if ierr != nil {
		return nil, ierr
	}

	// Find or create user based on Slack email
	user, ierr := s.findOrCreateSlackUser(slackUser)
	if ierr != nil {
		return nil, ierr
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

func (s authService) VerifyPassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func (s authService) GenerateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (s authService) ValidateToken(token string) (*models.User, core.IError) {
	userToken, ierr := repo.UserToken(s.ctx).FindOne("token = ?", token)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	user, ierr := repo.User(s.ctx).FindOne("id = ?", userToken.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	// Remove password from response
	user.Password = ""

	return user, nil
}

func (s authService) CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError {
	userToken := &models.UserToken{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserID:              userID,
		Token:               token,
		IPAddress:           ipAddress,
		UserAgent:           userAgent,
		DeviceInfo:          deviceInfo,
	}

	return repo.UserToken(s.ctx).Create(userToken)
}

func (s authService) DeleteUserToken(token string) core.IError {
	return repo.UserToken(s.ctx).Delete("token = ?", token)
}

// SlackUser represents the user data from Slack OAuth
type SlackUser struct {
	ID       string `json:"id"`
	Email    string `json:"email"`
	Name     string `json:"name"`
	RealName string `json:"real_name"`
	Image    string `json:"image_192"`
}

// SlackOAuthResponse represents the response from Slack OAuth token exchange
type SlackOAuthResponse struct {
	OK          bool   `json:"ok"`
	AccessToken string `json:"access_token"`
	Scope       string `json:"scope"`
	UserID      string `json:"user_id"`
	TeamID      string `json:"team_id"`
	Error       string `json:"error"`
}

// SlackUserResponse represents the response from Slack user.info API
type SlackUserResponse struct {
	OK   bool      `json:"ok"`
	User SlackUser `json:"user"`
}

func (s authService) exchangeSlackCode(code string) (*SlackUser, core.IError) {
	// Get Slack OAuth configuration from environment
	clientID := s.ctx.ENV().String("SLACK_CLIENT_ID")
	clientSecret := s.ctx.ENV().String("SLACK_CLIENT_SECRET")
	redirectURL := s.ctx.ENV().String("SLACK_REDIRECT_URL")

	if clientID == "" || clientSecret == "" {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_CONFIG_MISSING",
			Message: "Slack OAuth configuration is missing",
		}
		return nil, s.ctx.NewError(nil, ierr)
	}

	// Configure OAuth2
	config := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  redirectURL,
		Scopes:       []string{"openid", "profile", "email"},
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://slack.com/oauth/v2/authorize",
			TokenURL: "https://slack.com/api/oauth.v2.access",
		},
	}

	utils.LogStruct(config)

	// Exchange code for token
	ctx := context.Background()
	token, err := config.Exchange(ctx, code)
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "SLACK_TOKEN_EXCHANGE_FAILED",
			Message: "Failed to exchange Slack authorization code",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Get user info from Slack
	client := config.Client(ctx, token)
	resp, err := client.Get("https://slack.com/api/users.info?user=" + token.Extra("user_id").(string))
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_INFO_FAILED",
			Message: "Failed to get user info from Slack",
		}
		return nil, s.ctx.NewError(err, ierr)
	}
	defer resp.Body.Close()

	var userResp SlackUserResponse
	if err := json.NewDecoder(resp.Body).Decode(&userResp); err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_RESPONSE_PARSE_FAILED",
			Message: "Failed to parse Slack user response",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	if !userResp.OK {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "SLACK_API_ERROR",
			Message: "Slack API returned an error",
		}
		return nil, s.ctx.NewError(nil, ierr)
	}

	return &userResp.User, nil
}

func (s authService) findOrCreateSlackUser(slackUser *SlackUser) (*models.User, core.IError) {
	// Try to find existing user by email
	user, ierr := repo.User(s.ctx).Where("is_active = ?", true).FindOne("email = ?", slackUser.Email)
	if ierr != nil {
		if !errmsgs.IsNotFoundError(ierr) {
			return nil, s.ctx.NewError(ierr, nil)
		}

		// User doesn't exist, create new user
		displayName := slackUser.Name
		if displayName == "" {
			displayName = slackUser.RealName
		}

		// Generate a random password for Slack users (they won't use it)
		randomPassword, err := s.GenerateToken()
		if err != nil {
			ierr := core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "FAILED_TO_GENERATE_PASSWORD",
				Message: "Failed to generate password",
			}
			return nil, s.ctx.NewError(err, ierr)
		}

		hashedPassword, err := s.HashPassword(randomPassword)
		if err != nil {
			ierr := core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "FAILED_TO_HASH_PASSWORD",
				Message: "Failed to hash password",
			}
			return nil, s.ctx.NewError(err, ierr)
		}

		// Create new user
		newUser := &models.User{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			Email:               slackUser.Email,
			Password:            hashedPassword,
			FullName:            slackUser.RealName,
			DisplayName:         displayName,
			AvatarURL:           slackUser.Image,
			Role:                "USER",
			IsActive:            true,
		}

		ierr = repo.User(s.ctx).Create(newUser)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, nil)
		}

		// Remove password from response
		newUser.Password = ""
		return newUser, nil
	}

	// User exists, update avatar if provided
	if slackUser.Image != "" && user.AvatarURL != slackUser.Image {
		user.AvatarURL = slackUser.Image
		ierr = repo.User(s.ctx).Where("id = ?", user.ID).Updates(user)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, nil)
		}
	}

	// Remove password from response
	user.Password = ""
	return user, nil
}

func NewAuthService(ctx core.IContext) IAuthService {
	return &authService{ctx: ctx}
}
