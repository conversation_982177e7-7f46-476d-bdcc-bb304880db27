package services

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"golang.org/x/crypto/bcrypt"
)

type IAuthService interface {
	Login(input *LoginPayload) (*LoginResponse, core.IError)
	Logout(token string) core.IError
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) bool
	GenerateToken() (string, error)
	ValidateToken(token string) (*models.User, core.IError)
	CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError
	DeleteUserToken(token string) core.IError
}

type authService struct {
	ctx core.IContext
}

type LoginPayload struct {
	Email      string
	Password   string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type LoginResponse struct {
	User  *models.User `json:"user"`
	Token string       `json:"token"`
}

func (s authService) Login(input *LoginPayload) (*LoginResponse, core.IError) {
	// Find user by email
	user, ierr := repo.User(s.ctx).Where("is_active = ?", true).FindOne("email = ?", input.Email)
	if ierr != nil {
		if errmsgs.IsNotFoundError(ierr) {
			ierr = core.Error{
				Status:  http.StatusUnauthorized,
				Code:    "INVALID_CREDENTIALS",
				Message: "Invalid email or password",
			}
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Verify password
	if !s.VerifyPassword(user.Password, input.Password) {
		ierr = core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "INVALID_CREDENTIALS",
			Message: "Invalid email or password",
		}

		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) Logout(token string) core.IError {
	return s.DeleteUserToken(token)
}

func (s authService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

func (s authService) VerifyPassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func (s authService) GenerateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (s authService) ValidateToken(token string) (*models.User, core.IError) {
	userToken, ierr := repo.UserToken(s.ctx).FindOne("token = ?", token)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	user, ierr := repo.User(s.ctx).FindOne("id = ?", userToken.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	// Remove password from response
	user.Password = ""

	return user, nil
}

func (s authService) CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError {
	userToken := &models.UserToken{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserID:              userID,
		Token:               token,
		IPAddress:           ipAddress,
		UserAgent:           userAgent,
		DeviceInfo:          deviceInfo,
	}

	return repo.UserToken(s.ctx).Create(userToken)
}

func (s authService) DeleteUserToken(token string) core.IError {
	return repo.UserToken(s.ctx).Delete("token = ?", token)
}

func NewAuthService(ctx core.IContext) IAuthService {
	return &authService{ctx: ctx}
}
