package services

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"net/http"
	"net/url"

	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"golang.org/x/crypto/bcrypt"
)

type IAuthService interface {
	Login(input *LoginPayload) (*LoginResponse, core.IError)
	Logout(token string) core.IError
	SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError)
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) bool
	GenerateToken() (string, error)
	ValidateToken(token string) (*models.User, core.IError)
	CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError
	DeleteUserToken(token string) core.IError
}

type authService struct {
	ctx core.IContext
}

type LoginPayload struct {
	Email      string
	Password   string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type SlackCallbackPayload struct {
	Code       string
	State      string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type LoginResponse struct {
	User  *models.User `json:"user"`
	Token string       `json:"token"`
}

func (s authService) Login(input *LoginPayload) (*LoginResponse, core.IError) {
	// Find user by email
	user, ierr := repo.User(s.ctx).Where("is_active = ?", true).FindOne("email = ?", input.Email)
	if ierr != nil {
		if errmsgs.IsNotFoundError(ierr) {
			ierr = core.Error{
				Status:  http.StatusUnauthorized,
				Code:    "INVALID_CREDENTIALS",
				Message: "Invalid email or password",
			}
		}
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Verify password
	if !s.VerifyPassword(user.Password, input.Password) {
		ierr = core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "INVALID_CREDENTIALS",
			Message: "Invalid email or password",
		}

		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) Logout(token string) core.IError {
	return s.DeleteUserToken(token)
}

func (s authService) SlackCallback(input *SlackCallbackPayload) (*LoginResponse, core.IError) {
	// Exchange authorization code for access token
	slackUser, ierr := s.exchangeSlackCode(input.Code)
	if ierr != nil {
		return nil, ierr
	}

	// Find or create user based on Slack email
	user, ierr := s.findOrCreateSlackUser(slackUser)
	if ierr != nil {
		return nil, ierr
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		ierr = core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FAILED_TO_GENERATE_TOKEN",
			Message: "Failed to generate token",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

func (s authService) VerifyPassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func (s authService) GenerateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (s authService) ValidateToken(token string) (*models.User, core.IError) {
	userToken, ierr := repo.UserToken(s.ctx).FindOne("token = ?", token)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	user, ierr := repo.User(s.ctx).FindOne("id = ?", userToken.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, nil)
	}

	// Remove password from response
	user.Password = ""

	return user, nil
}

func (s authService) CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError {
	userToken := &models.UserToken{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserID:              userID,
		Token:               token,
		IPAddress:           ipAddress,
		UserAgent:           userAgent,
		DeviceInfo:          deviceInfo,
	}

	return repo.UserToken(s.ctx).Create(userToken)
}

func (s authService) DeleteUserToken(token string) core.IError {
	return repo.UserToken(s.ctx).Delete("token = ?", token)
}

// SlackUser represents the user data from Slack OAuth
type SlackUser struct {
	ID       string `json:"id"`
	Email    string `json:"email"`
	Name     string `json:"name"`
	RealName string `json:"real_name"`
	Image    string `json:"image_192"`
}

// SlackOAuthResponse represents the response from Slack OAuth token exchange
type SlackOAuthResponse struct {
	OK          bool   `json:"ok"`
	AccessToken string `json:"access_token"`
	Scope       string `json:"scope"`
	UserID      string `json:"user_id"`
	TeamID      string `json:"team_id"`
	Error       string `json:"error"`
}

// SlackUserResponse represents the response from Slack user.info API
type SlackUserResponse struct {
	OK   bool      `json:"ok"`
	User SlackUser `json:"user"`
}

func (s authService) exchangeSlackCode(code string) (*SlackUser, core.IError) {
	// Get Slack OAuth configuration from environment
	clientID := s.ctx.ENV().String("SLACK_CLIENT_ID")
	clientSecret := s.ctx.ENV().String("SLACK_CLIENT_SECRET")
	redirectURL := s.ctx.ENV().String("SLACK_REDIRECT_URL")

	if clientID == "" || clientSecret == "" {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_CONFIG_MISSING",
			Message: "Slack OAuth configuration is missing",
		}
		return nil, s.ctx.NewError(nil, ierr)
	}

	// Exchange code for token using custom implementation for Slack
	tokenResp, ierr := s.exchangeSlackToken(clientID, clientSecret, redirectURL, code)
	if ierr != nil {
		return nil, ierr
	}

	// Get user info from Slack using the access token
	slackUser, ierr := s.getSlackUserInfo(tokenResp.AccessToken)
	if ierr != nil {
		return nil, ierr
	}

	return slackUser, nil
}

func (s authService) exchangeSlackToken(clientID, clientSecret, redirectURL, code string) (*SlackOAuthResponse, core.IError) {
	// Prepare form data for Slack OAuth token exchange
	data := url.Values{}
	data.Set("client_id", clientID)
	data.Set("client_secret", clientSecret)
	data.Set("code", code)
	if redirectURL != "" {
		data.Set("redirect_uri", redirectURL)
	}

	// Make POST request to Slack OAuth endpoint
	resp, err := http.PostForm("https://slack.com/api/oauth.v2.access", data)
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_TOKEN_REQUEST_FAILED",
			Message: "Failed to make token request to Slack",
		}
		return nil, s.ctx.NewError(err, ierr)
	}
	defer resp.Body.Close()

	var tokenResp SlackOAuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_TOKEN_RESPONSE_PARSE_FAILED",
			Message: "Failed to parse Slack token response",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	if !tokenResp.OK {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "SLACK_TOKEN_EXCHANGE_FAILED",
			Message: "Slack token exchange failed: " + tokenResp.Error,
		}
		return nil, s.ctx.NewError(nil, ierr)
	}

	return &tokenResp, nil
}

func (s authService) getSlackUserInfo(accessToken string) (*SlackUser, core.IError) {
	// Create HTTP client
	client := &http.Client{}

	// Create request to get user info
	req, err := http.NewRequest("GET", "https://slack.com/api/users.identity", nil)
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_REQUEST_FAILED",
			Message: "Failed to create user info request",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	// Add authorization header
	req.Header.Set("Authorization", "Bearer "+accessToken)

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_INFO_FAILED",
			Message: "Failed to get user info from Slack",
		}
		return nil, s.ctx.NewError(err, ierr)
	}
	defer resp.Body.Close()

	// Parse response
	var userResp struct {
		OK   bool `json:"ok"`
		User struct {
			ID       string `json:"id"`
			Name     string `json:"name"`
			Email    string `json:"email"`
			Image192 string `json:"image_192"`
		} `json:"user"`
		Error string `json:"error"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&userResp); err != nil {
		ierr := core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "SLACK_USER_RESPONSE_PARSE_FAILED",
			Message: "Failed to parse Slack user response",
		}
		return nil, s.ctx.NewError(err, ierr)
	}

	if !userResp.OK {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "SLACK_USER_API_ERROR",
			Message: "Slack user API returned an error: " + userResp.Error,
		}
		return nil, s.ctx.NewError(nil, ierr)
	}

	// Convert to our SlackUser struct
	slackUser := &SlackUser{
		ID:       userResp.User.ID,
		Email:    userResp.User.Email,
		Name:     userResp.User.Name,
		RealName: userResp.User.Name, // users.identity doesn't have real_name, use name
		Image:    userResp.User.Image192,
	}

	return slackUser, nil
}

func (s authService) findOrCreateSlackUser(slackUser *SlackUser) (*models.User, core.IError) {
	// Try to find existing user by email
	user, ierr := repo.User(s.ctx).Where("is_active = ?", true).FindOne("email = ?", slackUser.Email)
	if ierr != nil {
		if !errmsgs.IsNotFoundError(ierr) {
			return nil, s.ctx.NewError(ierr, nil)
		}

		// User doesn't exist, create new user
		displayName := slackUser.Name
		if displayName == "" {
			displayName = slackUser.RealName
		}

		// Generate a random password for Slack users (they won't use it)
		randomPassword, err := s.GenerateToken()
		if err != nil {
			ierr := core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "FAILED_TO_GENERATE_PASSWORD",
				Message: "Failed to generate password",
			}
			return nil, s.ctx.NewError(err, ierr)
		}

		hashedPassword, err := s.HashPassword(randomPassword)
		if err != nil {
			ierr := core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "FAILED_TO_HASH_PASSWORD",
				Message: "Failed to hash password",
			}
			return nil, s.ctx.NewError(err, ierr)
		}

		// Create new user
		newUser := &models.User{
			BaseModelHardDelete: models.NewBaseModelHardDelete(),
			Email:               slackUser.Email,
			Password:            hashedPassword,
			FullName:            slackUser.RealName,
			DisplayName:         displayName,
			AvatarURL:           slackUser.Image,
			Role:                "USER",
			IsActive:            true,
		}

		ierr = repo.User(s.ctx).Create(newUser)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, nil)
		}

		// Remove password from response
		newUser.Password = ""
		return newUser, nil
	}

	// User exists, update avatar if provided
	if slackUser.Image != "" && user.AvatarURL != slackUser.Image {
		user.AvatarURL = slackUser.Image
		ierr = repo.User(s.ctx).Where("id = ?", user.ID).Updates(user)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, nil)
		}
	}

	// Remove password from response
	user.Password = ""
	return user, nil
}

func NewAuthService(ctx core.IContext) IAuthService {
	return &authService{ctx: ctx}
}
