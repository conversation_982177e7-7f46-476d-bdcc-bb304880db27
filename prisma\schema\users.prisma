// user.prisma
enum Role {
  USER
  ADMIN
  SUPER_ADMIN
}

model users {
  id           String  @id @default(uuid()) @db.Uuid
  email        String  @unique
  password     String
  full_name    String?
  display_name String?
  position     String?
  team         String? @db.Uuid
  avatar_url   String?
  role         Role    @default(USER)
  is_active    <PERSON><PERSON>an @default(true)

  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  access_tokens user_tokens[]

  @@index([email, team, role])
}
