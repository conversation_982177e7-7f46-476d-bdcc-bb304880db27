package me

import (
	"github.com/labstack/echo/v4"
	core "gitlab.finema.co/finema/idin-core"
)

func NewMeHTTP(e *echo.Echo, authMiddleware echo.MiddlewareFunc) {
	me := &MeController{}
	
	// Me routes - all require authentication
	e.GET("/me", core.WithHTTPContext(me.GetProfile), authMiddleware)
	e.PUT("/me", core.WithHTTPContext(me.UpdateProfile), authMiddleware)
	e.GET("/me/devices", core.WithHTTPContext(me.GetDevices), authMiddleware)
	e.DELETE("/me/devices/:id", core.WithHTTPContext(me.DeleteDevice), authMiddleware)
}
