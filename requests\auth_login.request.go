package requests

import core "gitlab.finema.co/finema/idin-core"

type AuthLogin struct {
	core.BaseValidator
	Email    *string `json:"email"`
	Password *string `json:"password"`
}

func (r *AuthLogin) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsEmail(r.<PERSON><PERSON>, "email"))
	r.Must(r.<PERSON>equired(r.<PERSON><PERSON>, "email"))
	r.Must(r.Is<PERSON>trRequired(r.Password, "password"))

	return r.Error()
}
